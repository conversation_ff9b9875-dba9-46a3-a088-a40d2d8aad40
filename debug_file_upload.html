<!DOCTYPE html>
<html>
<head>
    <title>Debug File Upload</title>
</head>
<body>
    <h1>Debug File Upload Test</h1>
    <input type="file" id="fileInput">
    <button onclick="testUpload()">Test Upload</button>
    <div id="result"></div>

    <script>
        async function testUpload() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('Please select a file');
                return;
            }

            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';

            try {
                console.log('Testing file upload with file:', file);
                
                const formData = new FormData();
                formData.append('myfile', file);
                
                console.log('FormData created with myfile field');
                console.log('File details:', {
                    name: file.name,
                    size: file.size,
                    type: file.type
                });

                const response = await fetch('https://fileforge-backend.vercel.app/api/files', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'Origin': 'https://fileforge-indol.vercel.app'
                    }
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', Object.fromEntries([...response.headers]));

                const responseText = await response.text();
                console.log('Response text:', responseText);

                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (e) {
                    data = { rawResponse: responseText };
                }

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <h3>✅ Upload Successful!</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h3>❌ Upload Failed (${response.status})</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }

            } catch (error) {
                console.error('Upload test failed:', error);
                resultDiv.innerHTML = `
                    <h3>❌ Network Error</h3>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
